/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/admin/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Ccomponents%5C%5CAdmin%5C%5Cadmin.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Ccomponents%5C%5CAdmin%5C%5Cadmin.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Admin/admin.tsx */ \"(app-pages-browser)/./src/components/Admin/admin.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGV3cGV3JTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDV29yayU1QkltcG9ydGFudCU1RCU1QyU1Q3NhbXBsZS1jYWZlLXdlYnNpdGUtMSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNBZG1pbiU1QyU1Q2FkbWluLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBaUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxwZXdwZXdcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxXb3JrW0ltcG9ydGFudF1cXFxcc2FtcGxlLWNhZmUtd2Vic2l0ZS0xXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXEFkbWluXFxcXGFkbWluLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Ccomponents%5C%5CAdmin%5C%5Cadmin.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGV3cGV3XFxPbmVEcml2ZVxcRGVza3RvcFxcV29ya1tJbXBvcnRhbnRdXFxzYW1wbGUtY2FmZS13ZWJzaXRlLTFcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Admin/LoginForm.tsx":
/*!********************************************!*\
  !*** ./src/components/Admin/LoginForm.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/RestaurantContext */ \"(app-pages-browser)/./src/context/RestaurantContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction LoginForm() {\n    _s();\n    const { login } = (0,_context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant)();\n    const [credentials, setCredentials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: ''\n    });\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        const success = login(credentials.username, credentials.password);\n        if (!success) {\n            setError('Invalid username or password');\n        } else {\n            setError('');\n        }\n    };\n    const handleChange = (e)=>{\n        setCredentials({\n            ...credentials,\n            [e.target.name]: e.target.value\n        });\n        setError('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"login-container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"login-form\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Admin Login\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"username\",\n                                    children: \"Username:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"username\",\n                                    name: \"username\",\n                                    value: credentials.username,\n                                    onChange: handleChange,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"form-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"password\",\n                                    children: \"Password:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    id: \"password\",\n                                    name: \"password\",\n                                    value: credentials.password,\n                                    onChange: handleChange,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"error-message\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"login-btn\",\n                            children: \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"default-credentials\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Default credentials:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 14\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Username: admin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Password: admin123\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\LoginForm.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginForm, \"hmtkVrNR8mv7atD0bMkmdl6eeQQ=\", false, function() {\n    return [\n        _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant\n    ];\n});\n_c = LoginForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginForm);\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Admin/LoginForm.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Admin/OrdersManagement.tsx":
/*!***************************************************!*\
  !*** ./src/components/Admin/OrdersManagement.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/RestaurantContext */ \"(app-pages-browser)/./src/context/RestaurantContext.tsx\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrdersManagement() {\n    _s();\n    const { orders, updateOrderStatus, getFilteredOrders, settings } = (0,_context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('queue');\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showConfirmation, setShowConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingStatusChange, setPendingStatusChange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pendingOrders = orders.filter((order)=>order.status === _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.PENDING);\n    const filteredOrders = getFilteredOrders(filters);\n    const handleStatusChange = (orderId, newStatus)=>{\n        setPendingStatusChange({\n            orderId,\n            status: newStatus\n        });\n        setShowConfirmation(true);\n    };\n    const confirmStatusChange = async ()=>{\n        if (pendingStatusChange) {\n            await updateOrderStatus(pendingStatusChange.orderId, pendingStatusChange.status);\n            setShowConfirmation(false);\n            setPendingStatusChange(null);\n        }\n    };\n    const cancelStatusChange = ()=>{\n        setShowConfirmation(false);\n        setPendingStatusChange(null);\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: settings.currency\n        }).format(amount);\n    };\n    const formatDate = (date)=>{\n        return new Date(date).toLocaleString();\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.PENDING:\n                return '#ff9800';\n            case _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.COMPLETED:\n                return '#4caf50';\n            case _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CANCELLED:\n                return '#f44336';\n            default:\n                return '#757575';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"orders-management\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"section-tabs\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"section-tab \".concat(activeSection === 'queue' ? 'active' : ''),\n                        onClick: ()=>setActiveSection('queue'),\n                        children: [\n                            \"Orders Queue (\",\n                            pendingOrders.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"section-tab \".concat(activeSection === 'history' ? 'active' : ''),\n                        onClick: ()=>setActiveSection('history'),\n                        children: \"Order History\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            activeSection === 'queue' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"orders-queue\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Current Pending Orders\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    pendingOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"no-orders\",\n                        children: \"No pending orders\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"orders-grid\",\n                        children: pendingOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"order-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"order-id\",\n                                                children: [\n                                                    \"#\",\n                                                    order.id.slice(-6)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"table-number\",\n                                                children: [\n                                                    \"Table \",\n                                                    order.tableNumber\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"order-status\",\n                                                style: {\n                                                    backgroundColor: getStatusColor(order.status)\n                                                },\n                                                children: order.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-time\",\n                                        children: formatDate(order.createdAt)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-items\",\n                                        children: order.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"order-item\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            item.quantity,\n                                                            \"x \",\n                                                            item.product.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatCurrency(item.price * item.quantity)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-total\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: [\n                                                \"Total: \",\n                                                formatCurrency(order.total)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleStatusChange(order.id, _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.COMPLETED),\n                                                className: \"complete-btn\",\n                                                children: \"Mark Complete\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleStatusChange(order.id, _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CANCELLED),\n                                                className: \"cancel-btn\",\n                                                children: \"Cancel Order\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, order.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this),\n            activeSection === 'history' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"orders-history\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Order History\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"filters\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"filter-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: filters.status || '',\n                                        onChange: (e)=>setFilters({\n                                                ...filters,\n                                                status: e.target.value || undefined\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All Statuses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.PENDING,\n                                                children: \"Pending\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.COMPLETED,\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CANCELLED,\n                                                children: \"Cancelled\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"filter-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Date:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: filters.date ? new Date(filters.date).toISOString().split('T')[0] : '',\n                                        onChange: (e)=>setFilters({\n                                                ...filters,\n                                                date: e.target.value ? new Date(e.target.value) : undefined\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"filter-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Table Number:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search table...\",\n                                        value: filters.tableNumber || '',\n                                        onChange: (e)=>setFilters({\n                                                ...filters,\n                                                tableNumber: e.target.value || undefined\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"filter-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Order ID:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search order ID...\",\n                                        value: filters.orderId || '',\n                                        onChange: (e)=>setFilters({\n                                                ...filters,\n                                                orderId: e.target.value || undefined\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setFilters({}),\n                                className: \"clear-filters-btn\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"orders-table\",\n                        children: filteredOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"no-orders\",\n                            children: \"No orders found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Order ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Table\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Items\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Total\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: filteredOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: [\n                                                        \"#\",\n                                                        order.id.slice(-6)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: order.tableNumber\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: [\n                                                        order.items.length,\n                                                        \" items\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: formatCurrency(order.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"status-badge\",\n                                                        style: {\n                                                            backgroundColor: getStatusColor(order.status)\n                                                        },\n                                                        children: order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: formatDate(order.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedOrder(order),\n                                                            className: \"view-btn\",\n                                                            children: \"View Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        order.status === _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.PENDING && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleStatusChange(order.id, _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.COMPLETED),\n                                                                    className: \"complete-btn small\",\n                                                                    children: \"Complete\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleStatusChange(order.id, _types__WEBPACK_IMPORTED_MODULE_3__.OrderStatus.CANCELLED),\n                                                                    className: \"cancel-btn small\",\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, order.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            selectedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: ()=>setSelectedOrder(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"modal-content\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: [\n                                        \"Order Details - #\",\n                                        selectedOrder.id.slice(-6)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedOrder(null),\n                                    className: \"close-btn\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"order-info\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Table:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" \",\n                                                selectedOrder.tableNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Status:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" \",\n                                                selectedOrder.status\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Created:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" \",\n                                                formatDate(selectedOrder.createdAt)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Updated:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" \",\n                                                formatDate(selectedOrder.updatedAt)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"order-items-detail\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Items:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedOrder.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"item-detail\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            item.quantity,\n                                                            \"x \",\n                                                            item.product.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            formatCurrency(item.price),\n                                                            \" each\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatCurrency(item.price * item.quantity)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"order-total-detail\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: [\n                                            \"Total: \",\n                                            formatCurrency(selectedOrder.total)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this),\n            showConfirmation && pendingStatusChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"modal-content confirmation-modal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Confirm Status Change\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-body\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Are you sure you want to change the order status to\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: pendingStatusChange.status\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"?\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-actions\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmStatusChange,\n                                    className: \"confirm-btn\",\n                                    children: \"Confirm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelStatusChange,\n                                    className: \"cancel-btn\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\OrdersManagement.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersManagement, \"QeI3tb4Cr3onCxuKELwhOu5pyeA=\", false, function() {\n    return [\n        _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant\n    ];\n});\n_c = OrdersManagement;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrdersManagement);\nvar _c;\n$RefreshReg$(_c, \"OrdersManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Admin/OrdersManagement.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Admin/ProductManagement.tsx":
/*!****************************************************!*\
  !*** ./src/components/Admin/ProductManagement.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/RestaurantContext */ \"(app-pages-browser)/./src/context/RestaurantContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ProductManagement() {\n    _s();\n    const { products, categories, addProduct, updateProduct, deleteProduct, addCategory, deleteCategory, settings } = (0,_context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('add');\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newCategory, setNewCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        price: 0,\n        category: '',\n        image: undefined\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData({\n            ...formData,\n            [name]: name === 'price' ? parseFloat(value) || 0 : value\n        });\n    };\n    const handleImageChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        setFormData({\n            ...formData,\n            image: file\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            if (editingProduct) {\n                await updateProduct(editingProduct.id, {\n                    name: formData.name,\n                    description: formData.description,\n                    price: formData.price,\n                    category: formData.category\n                });\n                setEditingProduct(null);\n            } else {\n                await addProduct(formData);\n            }\n            setFormData({\n                name: '',\n                description: '',\n                price: 0,\n                category: '',\n                image: undefined\n            });\n            // Reset file input\n            const fileInput = document.getElementById('image');\n            if (fileInput) fileInput.value = '';\n        } catch (error) {\n            console.error('Error saving product:', error);\n        }\n    };\n    const handleEdit = (product)=>{\n        setEditingProduct(product);\n        setFormData({\n            name: product.name,\n            description: product.description,\n            price: product.price,\n            category: product.category\n        });\n        setActiveSection('add');\n    };\n    const handleDelete = async (productId)=>{\n        await deleteProduct(productId);\n        setShowDeleteConfirm(null);\n    };\n    const handleAddCategory = async (e)=>{\n        e.preventDefault();\n        if (newCategory.trim()) {\n            await addCategory({\n                name: newCategory.trim()\n            });\n            setNewCategory('');\n        }\n    };\n    const handleDeleteCategory = async (categoryId)=>{\n        var _categories_find;\n        // Don't delete if products exist in this category\n        const categoryName = (_categories_find = categories.find((c)=>c.id === categoryId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name;\n        const hasProducts = products.some((p)=>p.category === categoryName);\n        if (hasProducts) {\n            alert('Cannot delete category that contains products. Please move or delete products first.');\n            return;\n        }\n        await deleteCategory(categoryId);\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: settings.currency\n        }).format(amount);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-management\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"section-tabs\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"section-tab \".concat(activeSection === 'add' ? 'active' : ''),\n                        onClick: ()=>setActiveSection('add'),\n                        children: editingProduct ? 'Edit Product' : 'Add Product'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"section-tab \".concat(activeSection === 'edit' ? 'active' : ''),\n                        onClick: ()=>setActiveSection('edit'),\n                        children: \"Manage Products\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"section-tab \".concat(activeSection === 'categories' ? 'active' : ''),\n                        onClick: ()=>setActiveSection('categories'),\n                        children: \"Categories\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            activeSection === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"add-product-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: editingProduct ? 'Edit Product' : 'Add New Product'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"product-form\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"name\",\n                                        children: \"Product Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"name\",\n                                        name: \"name\",\n                                        value: formData.name,\n                                        onChange: handleInputChange,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"description\",\n                                        children: \"Description:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"description\",\n                                        name: \"description\",\n                                        value: formData.description,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"price\",\n                                        children: [\n                                            \"Price (\",\n                                            settings.currency,\n                                            \"):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        id: \"price\",\n                                        name: \"price\",\n                                        value: formData.price,\n                                        onChange: handleInputChange,\n                                        step: \"0.01\",\n                                        min: \"0\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"category\",\n                                        children: \"Category:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"category\",\n                                        name: \"category\",\n                                        value: formData.category,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select a category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            categories.filter((cat)=>cat.name !== 'All Items').map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category.name,\n                                                    children: category.name\n                                                }, category.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"image\",\n                                        children: \"Product Image:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        id: \"image\",\n                                        name: \"image\",\n                                        onChange: handleImageChange,\n                                        accept: \"image/*\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                        children: \"Optional: Upload an image for the product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-actions\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"submit-btn\",\n                                        children: editingProduct ? 'Update Product' : 'Add Product'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setEditingProduct(null);\n                                            setFormData({\n                                                name: '',\n                                                description: '',\n                                                price: 0,\n                                                category: '',\n                                                image: undefined\n                                            });\n                                        },\n                                        className: \"cancel-btn\",\n                                        children: \"Cancel Edit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this),\n            activeSection === 'edit' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"manage-products-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: [\n                            \"Manage Products (\",\n                            products.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    products.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"no-products\",\n                        children: \"No products available. Add some products to get started!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"products-grid\",\n                        children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"product-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"product-info\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"product-description\",\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"product-category\",\n                                                children: [\n                                                    \"Category: \",\n                                                    product.category\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"product-price\",\n                                                children: formatCurrency(product.price)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"product-status\",\n                                                children: [\n                                                    \"Status: \",\n                                                    product.available ? 'Available' : 'Unavailable'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"product-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEdit(product),\n                                                className: \"edit-btn\",\n                                                children: \"Edit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>updateProduct(product.id, {\n                                                        available: !product.available\n                                                    }),\n                                                className: \"toggle-btn \".concat(product.available ? 'disable' : 'enable'),\n                                                children: product.available ? 'Disable' : 'Enable'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowDeleteConfirm(product.id),\n                                                className: \"delete-btn\",\n                                                children: \"Delete\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this),\n            activeSection === 'categories' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"categories-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Manage Categories\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"add-category\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Add New Category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleAddCategory,\n                                className: \"category-form\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newCategory,\n                                            onChange: (e)=>setNewCategory(e.target.value),\n                                            placeholder: \"Category name\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"add-btn\",\n                                            children: \"Add Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"categories-list\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Existing Categories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            categories.map((category)=>{\n                                const productCount = products.filter((p)=>p.category === category.name).length;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"category-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"category-info\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"category-name\",\n                                                    children: category.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"product-count\",\n                                                    children: [\n                                                        \"(\",\n                                                        productCount,\n                                                        \" products)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 19\n                                        }, this),\n                                        category.name !== 'All Items' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleDeleteCategory(category.id),\n                                            className: \"delete-category-btn\",\n                                            disabled: productCount > 0,\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, category.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this),\n            showDeleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"modal-content confirmation-modal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Confirm Delete\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-body\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Are you sure you want to delete this product? This action cannot be undone.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-actions\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleDelete(showDeleteConfirm),\n                                    className: \"confirm-btn\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteConfirm(null),\n                                    className: \"cancel-btn\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\ProductManagement.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductManagement, \"4K5rv7d9cJWppWDwlpSPMmhlIoI=\", false, function() {\n    return [\n        _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant\n    ];\n});\n_c = ProductManagement;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductManagement);\nvar _c;\n$RefreshReg$(_c, \"ProductManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Admin/ProductManagement.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Admin/SettingsPanel.tsx":
/*!************************************************!*\
  !*** ./src/components/Admin/SettingsPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/RestaurantContext */ \"(app-pages-browser)/./src/context/RestaurantContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SettingsPanel() {\n    _s();\n    const { settings, updateSettings, adminUser, updateAdminCredentials } = (0,_context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('restaurant');\n    const [restaurantForm, setRestaurantForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(settings);\n    const [adminForm, setAdminForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: adminUser.username,\n        password: '',\n        confirmPassword: ''\n    });\n    const [showPasswords, setShowPasswords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveStatus, setSaveStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const currencies = [\n        {\n            code: 'USD',\n            name: 'US Dollar',\n            symbol: '$'\n        },\n        {\n            code: 'EUR',\n            name: 'Euro',\n            symbol: '€'\n        },\n        {\n            code: 'GBP',\n            name: 'British Pound',\n            symbol: '£'\n        },\n        {\n            code: 'JPY',\n            name: 'Japanese Yen',\n            symbol: '¥'\n        },\n        {\n            code: 'CAD',\n            name: 'Canadian Dollar',\n            symbol: 'C$'\n        },\n        {\n            code: 'AUD',\n            name: 'Australian Dollar',\n            symbol: 'A$'\n        },\n        {\n            code: 'INR',\n            name: 'Indian Rupee',\n            symbol: '₹'\n        }\n    ];\n    const handleRestaurantSubmit = async (e)=>{\n        e.preventDefault();\n        setSaveStatus('saving');\n        try {\n            await updateSettings(restaurantForm);\n            setSaveStatus('saved');\n            setTimeout(()=>setSaveStatus('idle'), 2000);\n        } catch (error) {\n            setSaveStatus('error');\n            setTimeout(()=>setSaveStatus('idle'), 2000);\n        }\n    };\n    const handleAdminSubmit = async (e)=>{\n        e.preventDefault();\n        if (adminForm.password !== adminForm.confirmPassword) {\n            alert('Passwords do not match');\n            return;\n        }\n        if (adminForm.password.length < 6) {\n            alert('Password must be at least 6 characters long');\n            return;\n        }\n        setSaveStatus('saving');\n        try {\n            await updateAdminCredentials({\n                username: adminForm.username,\n                password: adminForm.password\n            });\n            setSaveStatus('saved');\n            setAdminForm({\n                username: adminForm.username,\n                password: '',\n                confirmPassword: ''\n            });\n            setTimeout(()=>setSaveStatus('idle'), 2000);\n        } catch (error) {\n            setSaveStatus('error');\n            setTimeout(()=>setSaveStatus('idle'), 2000);\n        }\n    };\n    const handleRestaurantChange = (e)=>{\n        const { name, value } = e.target;\n        setRestaurantForm({\n            ...restaurantForm,\n            [name]: name === 'taxRate' || name === 'serviceCharge' ? parseFloat(value) || 0 : value\n        });\n    };\n    const handleAdminChange = (e)=>{\n        const { name, value } = e.target;\n        setAdminForm({\n            ...adminForm,\n            [name]: value\n        });\n    };\n    const handleCurrencyChange = async (currencyCode)=>{\n        setSaveStatus('saving');\n        try {\n            await updateSettings({\n                currency: currencyCode\n            });\n            setRestaurantForm({\n                ...restaurantForm,\n                currency: currencyCode\n            });\n            setSaveStatus('saved');\n            setTimeout(()=>setSaveStatus('idle'), 2000);\n        } catch (error) {\n            setSaveStatus('error');\n            setTimeout(()=>setSaveStatus('idle'), 2000);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"settings-panel\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"section-tabs\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"section-tab \".concat(activeSection === 'restaurant' ? 'active' : ''),\n                        onClick: ()=>setActiveSection('restaurant'),\n                        children: \"Restaurant Details\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"section-tab \".concat(activeSection === 'admin' ? 'active' : ''),\n                        onClick: ()=>setActiveSection('admin'),\n                        children: \"Admin Account\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"section-tab \".concat(activeSection === 'currency' ? 'active' : ''),\n                        onClick: ()=>setActiveSection('currency'),\n                        children: \"Currency & Pricing\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            saveStatus !== 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"save-status \".concat(saveStatus),\n                children: [\n                    saveStatus === 'saving' && 'Saving...',\n                    saveStatus === 'saved' && 'Settings saved successfully!',\n                    saveStatus === 'error' && 'Error saving settings. Please try again.'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            activeSection === 'restaurant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"restaurant-settings\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Restaurant Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleRestaurantSubmit,\n                        className: \"settings-form\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"name\",\n                                        children: \"Restaurant Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"name\",\n                                        name: \"name\",\n                                        value: restaurantForm.name,\n                                        onChange: handleRestaurantChange,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"address\",\n                                        children: \"Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"address\",\n                                        name: \"address\",\n                                        value: restaurantForm.address,\n                                        onChange: handleRestaurantChange,\n                                        rows: 3,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"phone\",\n                                        children: \"Phone Number:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        id: \"phone\",\n                                        name: \"phone\",\n                                        value: restaurantForm.phone,\n                                        onChange: handleRestaurantChange,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"email\",\n                                        children: \"Email Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"email\",\n                                        name: \"email\",\n                                        value: restaurantForm.email,\n                                        onChange: handleRestaurantChange,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"save-btn\",\n                                disabled: saveStatus === 'saving',\n                                children: \"Save Restaurant Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this),\n            activeSection === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"admin-settings\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Admin Account Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleAdminSubmit,\n                        className: \"settings-form\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"username\",\n                                        children: \"Username:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"username\",\n                                        name: \"username\",\n                                        value: adminForm.username,\n                                        onChange: handleAdminChange,\n                                        required: true,\n                                        minLength: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"password\",\n                                        children: \"New Password:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: showPasswords ? 'text' : 'password',\n                                        id: \"password\",\n                                        name: \"password\",\n                                        value: adminForm.password,\n                                        onChange: handleAdminChange,\n                                        required: true,\n                                        minLength: 6,\n                                        placeholder: \"Enter new password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"confirmPassword\",\n                                        children: \"Confirm Password:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: showPasswords ? 'text' : 'password',\n                                        id: \"confirmPassword\",\n                                        name: \"confirmPassword\",\n                                        value: adminForm.confirmPassword,\n                                        onChange: handleAdminChange,\n                                        required: true,\n                                        minLength: 6,\n                                        placeholder: \"Confirm new password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"checkbox-label\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: showPasswords,\n                                            onChange: (e)=>setShowPasswords(e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Show passwords\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"save-btn\",\n                                disabled: saveStatus === 'saving',\n                                children: \"Update Admin Credentials\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"security-note\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Security Note:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Make sure to use a strong password and keep your credentials secure. You will need to log in again after changing your password.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this),\n            activeSection === 'currency' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"currency-settings\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Currency & Pricing Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"currency-selection\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Select Currency\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"currency-grid\",\n                                children: currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"currency-option \".concat(settings.currency === currency.code ? 'selected' : ''),\n                                        onClick: ()=>handleCurrencyChange(currency.code),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"currency-symbol\",\n                                                children: currency.symbol\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"currency-code\",\n                                                children: currency.code\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"currency-name\",\n                                                children: currency.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, currency.code, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleRestaurantSubmit,\n                        className: \"pricing-form\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Additional Charges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"taxRate\",\n                                        children: \"Tax Rate (%):\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        id: \"taxRate\",\n                                        name: \"taxRate\",\n                                        value: restaurantForm.taxRate * 100,\n                                        onChange: (e)=>setRestaurantForm({\n                                                ...restaurantForm,\n                                                taxRate: parseFloat(e.target.value) / 100 || 0\n                                            }),\n                                        step: \"0.01\",\n                                        min: \"0\",\n                                        max: \"100\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                        children: [\n                                            \"Current: \",\n                                            (restaurantForm.taxRate * 100).toFixed(2),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"serviceCharge\",\n                                        children: \"Service Charge (%):\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        id: \"serviceCharge\",\n                                        name: \"serviceCharge\",\n                                        value: restaurantForm.serviceCharge * 100,\n                                        onChange: (e)=>setRestaurantForm({\n                                                ...restaurantForm,\n                                                serviceCharge: parseFloat(e.target.value) / 100 || 0\n                                            }),\n                                        step: \"0.01\",\n                                        min: \"0\",\n                                        max: \"100\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                        children: [\n                                            \"Current: \",\n                                            (restaurantForm.serviceCharge * 100).toFixed(2),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"save-btn\",\n                                disabled: saveStatus === 'saving',\n                                children: \"Save Pricing Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pricing-preview\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Pricing Preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"preview-calculation\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"preview-line\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Sample Item Price:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: new Intl.NumberFormat('en-US', {\n                                                    style: 'currency',\n                                                    currency: settings.currency\n                                                }).format(10.00)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"preview-line\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Tax (\",\n                                                    (restaurantForm.taxRate * 100).toFixed(2),\n                                                    \"%):\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: new Intl.NumberFormat('en-US', {\n                                                    style: 'currency',\n                                                    currency: settings.currency\n                                                }).format(10.00 * restaurantForm.taxRate)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"preview-line\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Service Charge (\",\n                                                    (restaurantForm.serviceCharge * 100).toFixed(2),\n                                                    \"%):\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: new Intl.NumberFormat('en-US', {\n                                                    style: 'currency',\n                                                    currency: settings.currency\n                                                }).format(10.00 * restaurantForm.serviceCharge)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"preview-line total\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Total:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: new Intl.NumberFormat('en-US', {\n                                                        style: 'currency',\n                                                        currency: settings.currency\n                                                    }).format(10.00 * (1 + restaurantForm.taxRate + restaurantForm.serviceCharge))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\SettingsPanel.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPanel, \"IYpjf0jbWowuuec8S0aYVsqema0=\", false, function() {\n    return [\n        _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant\n    ];\n});\n_c = SettingsPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPanel);\nvar _c;\n$RefreshReg$(_c, \"SettingsPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Admin/SettingsPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Admin/admin.css":
/*!****************************************!*\
  !*** ./src/components/Admin/admin.css ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8f4642d42574\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FkbWluL2FkbWluLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGV3cGV3XFxPbmVEcml2ZVxcRGVza3RvcFxcV29ya1tJbXBvcnRhbnRdXFxzYW1wbGUtY2FmZS13ZWJzaXRlLTFcXHNyY1xcY29tcG9uZW50c1xcQWRtaW5cXGFkbWluLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhmNDY0MmQ0MjU3NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Admin/admin.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Admin/admin.tsx":
/*!****************************************!*\
  !*** ./src/components/Admin/admin.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/RestaurantContext */ \"(app-pages-browser)/./src/context/RestaurantContext.tsx\");\n/* harmony import */ var _Admin_OrdersManagement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Admin/OrdersManagement */ \"(app-pages-browser)/./src/components/Admin/OrdersManagement.tsx\");\n/* harmony import */ var _Admin_ProductManagement__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Admin/ProductManagement */ \"(app-pages-browser)/./src/components/Admin/ProductManagement.tsx\");\n/* harmony import */ var _Admin_SettingsPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Admin/SettingsPanel */ \"(app-pages-browser)/./src/components/Admin/SettingsPanel.tsx\");\n/* harmony import */ var _Admin_LoginForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Admin/LoginForm */ \"(app-pages-browser)/./src/components/Admin/LoginForm.tsx\");\n/* harmony import */ var _admin_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./admin.css */ \"(app-pages-browser)/./src/components/Admin/admin.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Admin() {\n    _s();\n    const { isAuthenticated, logout } = (0,_context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('orders');\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Admin_LoginForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n            lineNumber: 18,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"admin-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"admin-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        children: \"Restaurant Admin Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: logout,\n                        className: \"logout-btn\",\n                        children: \"Logout\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"admin-tabs\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"tab-btn \".concat(activeTab === 'orders' ? 'active' : ''),\n                        onClick: ()=>setActiveTab('orders'),\n                        children: \"Orders Management\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"tab-btn \".concat(activeTab === 'products' ? 'active' : ''),\n                        onClick: ()=>setActiveTab('products'),\n                        children: \"Product Management\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"tab-btn \".concat(activeTab === 'settings' ? 'active' : ''),\n                        onClick: ()=>setActiveTab('settings'),\n                        children: \"Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"admin-content\",\n                children: [\n                    activeTab === 'orders' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Admin_OrdersManagement__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 36\n                    }, this),\n                    activeTab === 'products' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Admin_ProductManagement__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 38\n                    }, this),\n                    activeTab === 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Admin_SettingsPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 38\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\Admin\\\\admin.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_s(Admin, \"SFtinma4OuewcKetdo7y4jV4Wq0=\", false, function() {\n    return [\n        _context_RestaurantContext__WEBPACK_IMPORTED_MODULE_2__.useRestaurant\n    ];\n});\n_c = Admin;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Admin);\nvar _c;\n$RefreshReg$(_c, \"Admin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Admin/admin.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/context/RestaurantContext.tsx":
/*!*******************************************!*\
  !*** ./src/context/RestaurantContext.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RestaurantProvider: () => (/* binding */ RestaurantProvider),\n/* harmony export */   useRestaurant: () => (/* binding */ useRestaurant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* __next_internal_client_entry_do_not_use__ RestaurantProvider,useRestaurant auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Initial state\nconst initialState = {\n    products: [\n        {\n            id: '1',\n            name: 'Classic Burger',\n            description: 'Juicy beef patty with lettuce, tomato, and our special sauce',\n            price: 12.99,\n            category: 'Burgers',\n            available: true,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: '2',\n            name: 'Chicken Caesar Salad',\n            description: 'Fresh romaine lettuce with grilled chicken, parmesan, and caesar dressing',\n            price: 10.99,\n            category: 'Salads',\n            available: true,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: '3',\n            name: 'French Fries',\n            description: 'Crispy golden fries seasoned with sea salt',\n            price: 4.99,\n            category: 'Sides',\n            available: true,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: '4',\n            name: 'Chocolate Cake',\n            description: 'Rich chocolate cake with chocolate frosting',\n            price: 6.99,\n            category: 'Desserts',\n            available: true,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        },\n        {\n            id: '5',\n            name: 'Coffee',\n            description: 'Freshly brewed coffee',\n            price: 2.99,\n            category: 'Drinks',\n            available: true,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }\n    ],\n    categories: [\n        {\n            id: '1',\n            name: 'All Items',\n            createdAt: new Date()\n        },\n        {\n            id: '2',\n            name: 'Burgers',\n            createdAt: new Date()\n        },\n        {\n            id: '3',\n            name: 'Sides',\n            createdAt: new Date()\n        },\n        {\n            id: '4',\n            name: 'Desserts',\n            createdAt: new Date()\n        },\n        {\n            id: '5',\n            name: 'Drinks',\n            createdAt: new Date()\n        }\n    ],\n    orders: [],\n    cart: {\n        items: [],\n        total: 0\n    },\n    settings: {\n        name: 'Sample Cafe',\n        address: '123 Main Street, City, State 12345',\n        phone: '+****************',\n        email: '<EMAIL>',\n        currency: 'USD',\n        taxRate: 0.08,\n        serviceCharge: 0.05\n    },\n    adminUser: {\n        username: 'admin',\n        password: 'admin123'\n    },\n    isAuthenticated: false\n};\n// Reducer\nfunction restaurantReducer(state, action) {\n    switch(action.type){\n        case 'SET_PRODUCTS':\n            return {\n                ...state,\n                products: action.payload\n            };\n        case 'ADD_PRODUCT':\n            return {\n                ...state,\n                products: [\n                    ...state.products,\n                    action.payload\n                ]\n            };\n        case 'UPDATE_PRODUCT':\n            return {\n                ...state,\n                products: state.products.map((product)=>product.id === action.payload.id ? {\n                        ...product,\n                        ...action.payload.product,\n                        updatedAt: new Date()\n                    } : product)\n            };\n        case 'DELETE_PRODUCT':\n            return {\n                ...state,\n                products: state.products.filter((product)=>product.id !== action.payload)\n            };\n        case 'ADD_CATEGORY':\n            return {\n                ...state,\n                categories: [\n                    ...state.categories,\n                    action.payload\n                ]\n            };\n        case 'DELETE_CATEGORY':\n            return {\n                ...state,\n                categories: state.categories.filter((category)=>category.id !== action.payload)\n            };\n        case 'SET_ORDERS':\n            return {\n                ...state,\n                orders: action.payload\n            };\n        case 'ADD_ORDER':\n            return {\n                ...state,\n                orders: [\n                    ...state.orders,\n                    action.payload\n                ]\n            };\n        case 'UPDATE_ORDER_STATUS':\n            return {\n                ...state,\n                orders: state.orders.map((order)=>order.id === action.payload.orderId ? {\n                        ...order,\n                        status: action.payload.status,\n                        updatedAt: new Date()\n                    } : order)\n            };\n        case 'ADD_TO_CART':\n            const existingItemIndex = state.cart.items.findIndex((item)=>item.productId === action.payload.product.id);\n            let newItems;\n            if (existingItemIndex >= 0) {\n                newItems = state.cart.items.map((item, index)=>index === existingItemIndex ? {\n                        ...item,\n                        quantity: item.quantity + action.payload.quantity\n                    } : item);\n            } else {\n                newItems = [\n                    ...state.cart.items,\n                    {\n                        productId: action.payload.product.id,\n                        product: action.payload.product,\n                        quantity: action.payload.quantity\n                    }\n                ];\n            }\n            const total = newItems.reduce((sum, item)=>sum + item.product.price * item.quantity, 0);\n            return {\n                ...state,\n                cart: {\n                    items: newItems,\n                    total\n                }\n            };\n        case 'UPDATE_CART_ITEM':\n            const updatedItems = state.cart.items.map((item)=>item.productId === action.payload.productId ? {\n                    ...item,\n                    quantity: action.payload.quantity\n                } : item).filter((item)=>item.quantity > 0);\n            const updatedTotal = updatedItems.reduce((sum, item)=>sum + item.product.price * item.quantity, 0);\n            return {\n                ...state,\n                cart: {\n                    items: updatedItems,\n                    total: updatedTotal\n                }\n            };\n        case 'REMOVE_FROM_CART':\n            const filteredItems = state.cart.items.filter((item)=>item.productId !== action.payload);\n            const filteredTotal = filteredItems.reduce((sum, item)=>sum + item.product.price * item.quantity, 0);\n            return {\n                ...state,\n                cart: {\n                    items: filteredItems,\n                    total: filteredTotal\n                }\n            };\n        case 'CLEAR_CART':\n            return {\n                ...state,\n                cart: {\n                    items: [],\n                    total: 0\n                }\n            };\n        case 'UPDATE_SETTINGS':\n            return {\n                ...state,\n                settings: {\n                    ...state.settings,\n                    ...action.payload\n                }\n            };\n        case 'UPDATE_ADMIN_CREDENTIALS':\n            return {\n                ...state,\n                adminUser: action.payload\n            };\n        case 'SET_AUTHENTICATED':\n            return {\n                ...state,\n                isAuthenticated: action.payload\n            };\n        case 'LOAD_STATE':\n            return {\n                ...state,\n                ...action.payload\n            };\n        default:\n            return state;\n    }\n}\n// Create context\nconst RestaurantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider component\nfunction RestaurantProvider(param) {\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(restaurantReducer, initialState);\n    // Load state from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RestaurantProvider.useEffect\": ()=>{\n            if (true) {\n                const savedState = localStorage.getItem('restaurantState');\n                if (savedState && savedState.trim()) {\n                    try {\n                        const parsedState = JSON.parse(savedState);\n                        // Convert date strings back to Date objects\n                        if (parsedState.products) {\n                            parsedState.products = parsedState.products.map({\n                                \"RestaurantProvider.useEffect\": (product)=>({\n                                        ...product,\n                                        createdAt: new Date(product.createdAt),\n                                        updatedAt: new Date(product.updatedAt)\n                                    })\n                            }[\"RestaurantProvider.useEffect\"]);\n                        }\n                        if (parsedState.orders) {\n                            parsedState.orders = parsedState.orders.map({\n                                \"RestaurantProvider.useEffect\": (order)=>({\n                                        ...order,\n                                        createdAt: new Date(order.createdAt),\n                                        updatedAt: new Date(order.updatedAt)\n                                    })\n                            }[\"RestaurantProvider.useEffect\"]);\n                        }\n                        if (parsedState.categories) {\n                            parsedState.categories = parsedState.categories.map({\n                                \"RestaurantProvider.useEffect\": (category)=>({\n                                        ...category,\n                                        createdAt: new Date(category.createdAt)\n                                    })\n                            }[\"RestaurantProvider.useEffect\"]);\n                        }\n                        dispatch({\n                            type: 'LOAD_STATE',\n                            payload: parsedState\n                        });\n                    } catch (error) {\n                        console.error('Error loading saved state:', error);\n                        // Clear corrupted localStorage data\n                        localStorage.removeItem('restaurantState');\n                    }\n                }\n            }\n        }\n    }[\"RestaurantProvider.useEffect\"], []);\n    // Save state to localStorage whenever state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RestaurantProvider.useEffect\": ()=>{\n            if (true) {\n                localStorage.setItem('restaurantState', JSON.stringify(state));\n            }\n        }\n    }[\"RestaurantProvider.useEffect\"], [\n        state\n    ]);\n    // Utility function to generate unique IDs\n    const generateId = ()=>Date.now().toString() + Math.random().toString(36).substr(2, 9);\n    // Context value\n    const contextValue = {\n        // Products\n        products: state.products,\n        categories: state.categories,\n        addProduct: async (productData)=>{\n            const newProduct = {\n                id: generateId(),\n                name: productData.name,\n                description: productData.description,\n                price: productData.price,\n                category: productData.category,\n                available: true,\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            dispatch({\n                type: 'ADD_PRODUCT',\n                payload: newProduct\n            });\n        },\n        updateProduct: async (id, product)=>{\n            dispatch({\n                type: 'UPDATE_PRODUCT',\n                payload: {\n                    id,\n                    product\n                }\n            });\n        },\n        deleteProduct: async (id)=>{\n            dispatch({\n                type: 'DELETE_PRODUCT',\n                payload: id\n            });\n        },\n        addCategory: async (category)=>{\n            const newCategory = {\n                id: generateId(),\n                ...category,\n                createdAt: new Date()\n            };\n            dispatch({\n                type: 'ADD_CATEGORY',\n                payload: newCategory\n            });\n        },\n        deleteCategory: async (id)=>{\n            dispatch({\n                type: 'DELETE_CATEGORY',\n                payload: id\n            });\n        },\n        // Orders\n        orders: state.orders,\n        updateOrderStatus: async (orderId, status)=>{\n            dispatch({\n                type: 'UPDATE_ORDER_STATUS',\n                payload: {\n                    orderId,\n                    status\n                }\n            });\n        },\n        getFilteredOrders: (filters)=>{\n            return state.orders.filter((order)=>{\n                if (filters.status && order.status !== filters.status) return false;\n                if (filters.tableNumber && !order.tableNumber.includes(filters.tableNumber)) return false;\n                if (filters.orderId && !order.id.includes(filters.orderId)) return false;\n                if (filters.date) {\n                    const orderDate = new Date(order.createdAt);\n                    const filterDate = new Date(filters.date);\n                    if (orderDate.toDateString() !== filterDate.toDateString()) return false;\n                }\n                if (filters.month !== undefined && filters.year !== undefined) {\n                    const orderDate = new Date(order.createdAt);\n                    if (orderDate.getMonth() !== filters.month || orderDate.getFullYear() !== filters.year) return false;\n                }\n                return true;\n            });\n        },\n        // Cart\n        cart: state.cart,\n        addToCart: (product, quantity)=>{\n            dispatch({\n                type: 'ADD_TO_CART',\n                payload: {\n                    product,\n                    quantity\n                }\n            });\n        },\n        updateCartItem: (productId, quantity)=>{\n            dispatch({\n                type: 'UPDATE_CART_ITEM',\n                payload: {\n                    productId,\n                    quantity\n                }\n            });\n        },\n        removeFromCart: (productId)=>{\n            dispatch({\n                type: 'REMOVE_FROM_CART',\n                payload: productId\n            });\n        },\n        clearCart: ()=>{\n            dispatch({\n                type: 'CLEAR_CART'\n            });\n        },\n        createOrder: async (tableNumber)=>{\n            const newOrder = {\n                id: generateId(),\n                items: state.cart.items.map((item)=>({\n                        productId: item.productId,\n                        product: item.product,\n                        quantity: item.quantity,\n                        price: item.product.price\n                    })),\n                tableNumber,\n                status: _types__WEBPACK_IMPORTED_MODULE_2__.OrderStatus.PENDING,\n                total: state.cart.total,\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            dispatch({\n                type: 'ADD_ORDER',\n                payload: newOrder\n            });\n            dispatch({\n                type: 'CLEAR_CART'\n            });\n        },\n        // Settings\n        settings: state.settings,\n        updateSettings: async (settings)=>{\n            dispatch({\n                type: 'UPDATE_SETTINGS',\n                payload: settings\n            });\n        },\n        // Admin\n        adminUser: state.adminUser,\n        isAuthenticated: state.isAuthenticated,\n        updateAdminCredentials: async (credentials)=>{\n            dispatch({\n                type: 'UPDATE_ADMIN_CREDENTIALS',\n                payload: credentials\n            });\n        },\n        login: (username, password)=>{\n            if (username === state.adminUser.username && password === state.adminUser.password) {\n                dispatch({\n                    type: 'SET_AUTHENTICATED',\n                    payload: true\n                });\n                return true;\n            }\n            return false;\n        },\n        logout: ()=>{\n            dispatch({\n                type: 'SET_AUTHENTICATED',\n                payload: false\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RestaurantContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\context\\\\RestaurantContext.tsx\",\n        lineNumber: 416,\n        columnNumber: 5\n    }, this);\n}\n_s(RestaurantProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = RestaurantProvider;\n// Custom hook to use the context\nfunction useRestaurant() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(RestaurantContext);\n    if (context === undefined) {\n        throw new Error('useRestaurant must be used within a RestaurantProvider');\n    }\n    return context;\n}\n_s1(useRestaurant, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"RestaurantProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/RestaurantContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderStatus: () => (/* binding */ OrderStatus)\n/* harmony export */ });\n// Product related types\nvar OrderStatus = /*#__PURE__*/ function(OrderStatus) {\n    OrderStatus[\"PENDING\"] = \"pending\";\n    OrderStatus[\"COMPLETED\"] = \"completed\";\n    OrderStatus[\"CANCELLED\"] = \"cancelled\";\n    return OrderStatus;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Ccomponents%5C%5CAdmin%5C%5Cadmin.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);