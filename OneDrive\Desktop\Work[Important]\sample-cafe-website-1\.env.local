# ========================================
# SAMPLE CAFE WEBSITE - ENVIRONMENT CONFIG
# ========================================

# Firebase Configuration
# Get these from your Firebase Console > Project Settings > General > Your apps
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBUzV9cj6lxAy7CayVfo3-MQk5_ZJoNRx0
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=sample-cafe-website.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=sample-cafe-website
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=sample-cafe-website.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=946719851074
NEXT_PUBLIC_FIREBASE_APP_ID=1:946719851074:web:94420bf73039a4fd2423db

# Application Settings
NEXT_PUBLIC_APP_NAME="Sample Cafe"
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Firebase Admin Setup
NEXT_PUBLIC_ENABLE_ADMIN_SETUP=true

# Environment Settings
NODE_ENV=development

# Security Settings (Generate your own secure keys)
JWT_SECRET=ICdUbl2BBlU3McOr2JtAdm7h3rPc0VVVS8tqL5n0I5L7gNjBZX4UXTz/JjeftfFJ6blFez0RkejCI+OPeDX9Uw==
ENCRYPTION_KEY=5277dc9cc0d76b486d5499ce72a95002b06c341d86b1f4502f74133f011b098c

# Local Image Storage Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Legacy Vercel Blob Storage (can be removed after migration)
# BLOB_READ_WRITE_TOKEN=vercel_blob_rw_token_placeholder

# Legacy Admin Authentication (Backup - Firebase Auth is primary)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Optional: Social Media Links
NEXT_PUBLIC_FACEBOOK_URL=https://facebook.com/samplecafe
NEXT_PUBLIC_INSTAGRAM_URL=https://instagram.com/samplecafe
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/samplecafe

# Optional: Contact Information
NEXT_PUBLIC_PHONE=******-CAFE-123
NEXT_PUBLIC_EMAIL=<EMAIL>
NEXT_PUBLIC_ADDRESS="123 Coffee Street, Brew City, BC 12345"

# Optional: Business Hours
NEXT_PUBLIC_HOURS_WEEKDAY="7:00 AM - 9:00 PM"
NEXT_PUBLIC_HOURS_WEEKEND="8:00 AM - 10:00 PM"
