/* CSS Variables for Theme Consistency */
:root {
  --admin-primary: #ff9f1c;
  --admin-primary-light: #ffb54f;
  --admin-primary-dark: #e67e22;
  --admin-secondary: #2c3e50;
  --admin-background: #fff8f0;
  --admin-white: #ffffff;
  --admin-gray-light: #f8f9fa;
  --admin-gray: #666666;
  --admin-gray-dark: #333333;
  --admin-border: #e9ecef;
  --admin-shadow: 0 4px 12px rgba(255, 159, 28, 0.15);
  --admin-shadow-hover: 0 8px 25px rgba(255, 159, 28, 0.25);
  --admin-danger: #dc3545;
  --admin-success: #28a745;
  --admin-warning: #ffc107;
  --admin-info: #17a2b8;
  --admin-gradient: linear-gradient(135deg, var(--admin-background) 0%, #fff 50%, var(--admin-background) 100%);
  --admin-card-shadow: 0 8px 32px rgba(255, 159, 28, 0.12);
  --admin-card-shadow-hover: 0 12px 40px rgba(255, 159, 28, 0.18);
}

/* Admin Container */
.admin-container {
  min-height: 100vh;
  background: var(--admin-gradient);
  padding: 0;
  font-family: 'Arial', sans-serif;
  position: relative;
}

.admin-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 159, 28, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(44, 62, 80, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.admin-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: var(--admin-card-shadow);
  padding: 2rem 3rem;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(255, 159, 28, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 70px;
  z-index: 100;
  border-radius: 0 0 20px 20px;
}

.admin-header h1 {
  color: var(--admin-secondary);
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--admin-secondary) 0%, var(--admin-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.admin-header h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  border-radius: 2px;
}

.logout-btn {
  background: linear-gradient(135deg, var(--admin-danger) 0%, #e74c3c 100%);
  color: var(--admin-white);
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4);
}

/* Tabs */
.admin-tabs, .section-tabs {
  display: flex;
  gap: 0;
  margin: 0;
  padding: 0 3rem;
  background: var(--admin-white);
  border-bottom: 1px solid var(--admin-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab-btn, .section-tab {
  background: none;
  border: none;
  padding: 18px 32px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: var(--admin-gray);
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tab-btn::before, .section-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.tab-btn:hover, .section-tab:hover {
  color: var(--admin-secondary);
  background: var(--admin-gray-light);
  transform: translateY(-1px);
}

.tab-btn.active, .section-tab.active {
  color: var(--admin-primary);
  border-bottom-color: var(--admin-primary);
  background: var(--admin-gray-light);
  font-weight: 600;
}

.tab-btn.active::before, .section-tab.active::before {
  opacity: 0.1;
}

/* Login Form */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--admin-background) 0%, #fff 50%, var(--admin-background) 100%);
  padding: 2rem;
}

.login-form {
  background: var(--admin-white);
  padding: 3rem;
  border-radius: 20px;
  box-shadow: var(--admin-shadow-hover);
  width: 100%;
  max-width: 450px;
  border: 1px solid rgba(255, 159, 28, 0.1);
  position: relative;
  overflow: hidden;
}

.login-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
}

.login-form h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--admin-secondary);
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--admin-secondary) 0%, var(--admin-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.default-credentials {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.1) 0%, rgba(255, 181, 79, 0.1) 100%);
  border-radius: 12px;
  font-size: 14px;
  border: 1px solid rgba(255, 159, 28, 0.2);
  color: var(--admin-gray-dark);
}

.default-credentials p {
  margin: 0.5rem 0;
}

.default-credentials strong {
  color: var(--admin-primary);
}

.default-credentials p {
  margin: 5px 0;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.form-group small {
  display: block;
  margin-top: 5px;
  color: #666;
  font-size: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
}

/* Buttons */
.login-btn, .submit-btn, .save-btn, .confirm-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
  width: 100%;
}

.login-btn:hover, .submit-btn:hover, .save-btn:hover, .confirm-btn:hover {
  background-color: #1976d2;
}

.cancel-btn {
  background-color: #757575;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.cancel-btn:hover {
  background-color: #616161;
}

.edit-btn {
  background-color: #ff9800;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
}

.edit-btn:hover {
  background-color: #f57c00;
}

.delete-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

.complete-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
}

.complete-btn:hover {
  background-color: #388e3c;
}

.complete-btn.small {
  padding: 4px 8px;
  font-size: 12px;
}

.cancel-btn.small {
  padding: 4px 8px;
  font-size: 12px;
}

.view-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;
}

.view-btn:hover {
  background-color: #1976d2;
}

.toggle-btn {
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
}

.toggle-btn.enable {
  background-color: #4caf50;
  color: white;
}

.toggle-btn.enable:hover {
  background-color: #388e3c;
}

.toggle-btn.disable {
  background-color: #ff9800;
  color: white;
}

.toggle-btn.disable:hover {
  background-color: #f57c00;
}

.add-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  margin-left: 10px;
}

.add-btn:hover {
  background-color: #388e3c;
}

.clear-filters-btn {
  background-color: #757575;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.clear-filters-btn:hover {
  background-color: #616161;
}

.delete-category-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.delete-category-btn:hover:not(:disabled) {
  background-color: #d32f2f;
}

.delete-category-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Error Message */
.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  border: 1px solid #ffcdd2;
}

/* Save Status */
.save-status {
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: bold;
}

.save-status.saving {
  background-color: #fff3e0;
  color: #ef6c00;
  border: 1px solid #ffcc02;
}

.save-status.saved {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.save-status.error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #f44336;
}

/* Orders Management */
.orders-management {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.orders-queue h3, .orders-history h3 {
  margin-bottom: 20px;
  color: #333;
}

.no-orders {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.order-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;
}

.order-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.order-id {
  font-weight: bold;
  color: #333;
}

.table-number {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.order-status {
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.order-time {
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;
}

.order-items {
  margin-bottom: 15px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-total {
  text-align: right;
  margin-bottom: 15px;
  font-size: 16px;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.order-actions button {
  flex: 1;
  padding: 8px 12px;
  font-size: 14px;
}

/* Filters */
.filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.filter-group input,
.filter-group select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* Orders Table */
.orders-table {
  overflow-x: auto;
}

.orders-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.orders-table th,
.orders-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.orders-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

.orders-table tr:hover {
  background-color: #f8f9fa;
}

.status-badge {
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

/* Product Management */
.product-management {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.form-actions button {
  flex: 1;
}

.no-products {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.product-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;
}

.product-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.product-description {
  color: #666;
  margin-bottom: 10px;
  font-size: 14px;
}

.product-category,
.product-price,
.product-status {
  margin-bottom: 8px;
  font-size: 14px;
}

.product-price {
  font-weight: bold;
  color: #2196f3;
  font-size: 16px;
}

.product-actions {
  display: flex;
  gap: 8px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.product-actions button {
  flex: 1;
  min-width: 80px;
}

/* Categories */
.categories-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-category {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.category-form {
  display: flex;
  align-items: end;
  gap: 10px;
}

.category-form .form-group {
  flex: 1;
  margin-bottom: 0;
}

.categories-list h4 {
  margin-bottom: 15px;
  color: #333;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-name {
  font-weight: bold;
  color: #333;
}

.product-count {
  color: #666;
  font-size: 14px;
}

/* Settings Panel */
.settings-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-form {
  max-width: 600px;
  margin: 0 auto;
}

.security-note {
  margin-top: 30px;
  padding: 15px;
  background-color: #fff3e0;
  border-radius: 6px;
  border-left: 4px solid #ff9800;
}

.security-note h4 {
  margin: 0 0 10px 0;
  color: #ef6c00;
}

.security-note p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* Currency Settings */
.currency-selection {
  margin-bottom: 30px;
}

.currency-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.currency-option {
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s;
  background: white;
}

.currency-option:hover {
  border-color: #2196f3;
  background-color: #f8f9fa;
}

.currency-option.selected {
  border-color: #2196f3;
  background-color: #e3f2fd;
}

.currency-symbol {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2196f3;
  margin-bottom: 5px;
}

.currency-code {
  display: block;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.currency-name {
  display: block;
  font-size: 14px;
  color: #666;
}

.pricing-form {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.pricing-preview {
  margin-top: 30px;
  padding: 20px;
  background-color: #e8f5e8;
  border-radius: 8px;
  border: 1px solid #4caf50;
}

.preview-calculation {
  max-width: 300px;
}

.preview-line {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #ddd;
}

.preview-line:last-child {
  border-bottom: none;
}

.preview-line.total {
  border-top: 2px solid #333;
  margin-top: 10px;
  padding-top: 10px;
  font-size: 16px;
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.close-btn:hover {
  background-color: #e0e0e0;
}

.modal-body {
  padding: 20px;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.modal-actions button {
  width: auto;
  min-width: 100px;
}

.confirmation-modal {
  max-width: 400px;
}

.confirmation-modal .modal-body {
  text-align: center;
  padding: 30px 20px;
}

.confirmation-modal .modal-body p {
  margin: 0;
  font-size: 16px;
  color: #333;
}

/* Order Details Modal */
.order-info {
  margin-bottom: 20px;
}

.order-info p {
  margin: 8px 0;
  font-size: 14px;
}

.order-items-detail h4 {
  margin-bottom: 15px;
  color: #333;
}

.item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.item-detail:last-child {
  border-bottom: none;
}

.order-total-detail {
  text-align: right;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 2px solid #e0e0e0;
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-container {
    padding: 10px;
  }

  .admin-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .admin-tabs, .section-tabs {
    flex-wrap: wrap;
    justify-content: center;
  }

  .orders-grid,
  .products-grid {
    grid-template-columns: 1fr;
  }

  .filters {
    grid-template-columns: 1fr;
  }

  .currency-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .modal-content {
    width: 95%;
    margin: 10px;
  }

  .form-actions {
    flex-direction: column;
  }

  .modal-actions {
    flex-direction: column;
  }

  .product-actions {
    flex-direction: column;
  }

  .order-actions {
    flex-direction: column;
  }

  .category-form {
    flex-direction: column;
    align-items: stretch;
  }

  .category-item {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
}