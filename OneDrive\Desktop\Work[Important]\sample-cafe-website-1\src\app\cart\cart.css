.cart-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20px 0;
}

.cart-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.cart-container h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 2.5rem;
}

.empty-cart {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.empty-cart p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 20px;
}

.continue-shopping-btn {
  display: inline-block;
  background-color: #007bff;
  color: white;
  padding: 12px 24px;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.continue-shopping-btn:hover {
  background-color: #0056b3;
}

.order-success {
  background-color: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #c3e6cb;
}

.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  align-items: start;
}

.cart-items {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.2rem;
}

.item-info p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 0.9rem;
}

.item-price {
  font-weight: bold;
  color: #007bff;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f8f9fa;
  border-radius: 5px;
  padding: 5px;
}

.quantity-btn {
  background: #007bff;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover {
  background: #0056b3;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: bold;
}

.item-total {
  font-weight: bold;
  color: #333;
  min-width: 80px;
  text-align: right;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
}

.remove-btn:hover {
  background: #c82333;
}

.cart-summary {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 20px;
}

.cart-summary h3 {
  margin: 0 0 20px 0;
  color: #333;
  text-align: center;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
}

.summary-line.total {
  border-top: 2px solid #eee;
  margin-top: 15px;
  padding-top: 15px;
  font-weight: bold;
  font-size: 1.1rem;
  color: #333;
}

.order-form {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #333;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
}

.cart-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.clear-cart-btn,
.place-order-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.clear-cart-btn {
  background: #6c757d;
  color: white;
}

.clear-cart-btn:hover {
  background: #5a6268;
}

.place-order-btn {
  background: #28a745;
  color: white;
}

.place-order-btn:hover:not(:disabled) {
  background: #218838;
}

.place-order-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cart-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .cart-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .item-controls {
    width: 100%;
    justify-content: space-between;
  }

  .cart-actions {
    flex-direction: column;
  }

  .cart-container {
    padding: 0 10px;
  }

  .cart-container h1 {
    font-size: 2rem;
  }
}
