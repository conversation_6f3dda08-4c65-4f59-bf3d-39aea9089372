/* Cart Container */
.cart-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: 'Arial', sans-serif;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.cart-header h2 {
  color: #333;
  margin: 0;
  font-size: 2rem;
}

.clear-cart-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.clear-cart-btn:hover {
  background-color: #d32f2f;
}

/* Empty Cart */
.empty-cart {
  text-align: center;
  padding: 80px 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #e0e0e0;
}

.empty-cart-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-cart h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.empty-cart p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

/* Cart Content */
.cart-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
}

/* Cart Items */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cart-item {
  display: grid;
  grid-template-columns: 120px 1fr auto;
  gap: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  transition: box-shadow 0.3s;
}

.cart-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.item-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
}

.placeholder-image span {
  font-size: 2rem;
  opacity: 0.5;
}

.item-details {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.item-description {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.item-category {
  display: inline-block;
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 8px;
  width: fit-content;
}

.item-price {
  font-size: 1.1rem;
  font-weight: bold;
  color: #2196f3;
}

.item-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  min-width: 120px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 5px;
}

.quantity-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.quantity-btn:hover:not(:disabled) {
  background-color: #1976d2;
}

.quantity-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.quantity {
  font-weight: bold;
  color: #333;
  min-width: 30px;
  text-align: center;
}

.item-total {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.remove-btn {
  background-color: #f44336;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.remove-btn:hover {
  background-color: #d32f2f;
}

/* Cart Summary */
.cart-summary {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  height: fit-content;
  position: sticky;
  top: 20px;
}

.summary-section {
  padding: 25px;
  border-bottom: 1px solid #e0e0e0;
}

.summary-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.summary-line:last-child {
  border-bottom: none;
}

.summary-line.total {
  border-top: 2px solid #333;
  margin-top: 15px;
  padding-top: 15px;
  font-size: 1.1rem;
}

.checkout-section {
  padding: 25px;
}

.checkout-btn {
  width: 100%;
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.checkout-btn:hover {
  background-color: #388e3c;
}

.checkout-note {
  margin: 15px 0 0 0;
  color: #666;
  font-size: 0.85rem;
  text-align: center;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cart-container {
    padding: 20px 15px;
  }
  
  .cart-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .cart-header h2 {
    font-size: 1.5rem;
  }
  
  .cart-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .cart-item {
    grid-template-columns: 80px 1fr;
    grid-template-rows: auto auto;
    gap: 15px;
  }
  
  .item-image {
    width: 80px;
    height: 80px;
  }
  
  .item-controls {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .empty-cart {
    padding: 60px 20px;
  }
  
  .empty-cart-icon {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .cart-item {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .item-image {
    justify-self: center;
  }
  
  .item-controls {
    justify-content: center;
    gap: 20px;
  }
  
  .quantity-controls {
    order: 1;
  }
  
  .item-total {
    order: 2;
  }
  
  .remove-btn {
    order: 3;
  }
}

/* Checkout Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.close-btn:hover {
  background-color: #e0e0e0;
}

.modal-body {
  padding: 25px;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 25px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.modal-actions button {
  min-width: 120px;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.cancel-btn {
  background-color: #757575;
  color: white;
}

.cancel-btn:hover {
  background-color: #616161;
}

.confirm-btn {
  background-color: #4caf50;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background-color: #388e3c;
}

.confirm-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Checkout Form */
.checkout-form {
  display: grid;
  gap: 30px;
}

.form-section h4,
.order-review h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Order Review */
.review-items {
  margin-bottom: 20px;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.review-item:last-child {
  border-bottom: none;
}

.item-name {
  color: #333;
  font-weight: 500;
}

.item-price {
  color: #2196f3;
  font-weight: bold;
}

.review-summary {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.review-summary .summary-line {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.review-summary .summary-line:last-child {
  border-bottom: none;
}

.review-summary .summary-line.total {
  border-top: 2px solid #333;
  margin-top: 15px;
  padding-top: 15px;
  font-size: 1.1rem;
}

/* Order Confirmation */
.order-confirmation {
  text-align: center;
  padding: 20px;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.order-confirmation h4 {
  color: #4caf50;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.order-confirmation p {
  margin: 10px 0;
  color: #333;
}

.order-status {
  background-color: #e8f5e8;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #4caf50;
}

.order-status p {
  margin: 8px 0;
  color: #2e7d32;
  line-height: 1.5;
}

/* Responsive Modal */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header,
  .modal-body,
  .modal-actions {
    padding: 20px;
  }

  .modal-actions {
    flex-direction: column;
  }

  .modal-actions button {
    width: 100%;
  }

  .checkout-form {
    gap: 25px;
  }
}
