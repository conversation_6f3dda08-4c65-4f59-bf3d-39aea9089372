{"name": "sample-cafe-website", "version": 2, "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_FIREBASE_API_KEY": "@firebase_api_key", "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN": "@firebase_auth_domain", "NEXT_PUBLIC_FIREBASE_PROJECT_ID": "@firebase_project_id", "NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET": "@firebase_storage_bucket", "NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "@firebase_messaging_sender_id", "NEXT_PUBLIC_FIREBASE_APP_ID": "@firebase_app_id", "NEXT_PUBLIC_APP_NAME": "@app_name", "NEXT_PUBLIC_APP_URL": "@app_url", "NEXT_PUBLIC_ENABLE_ADMIN_SETUP": "@enable_admin_setup", "JWT_SECRET": "@jwt_secret", "ENCRYPTION_KEY": "@encryption_key", "NODE_ENV": "production"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/admin/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "redirects": [{"source": "/admin", "destination": "/admin/", "permanent": true}], "regions": ["iad1"], "github": {"silent": true}}